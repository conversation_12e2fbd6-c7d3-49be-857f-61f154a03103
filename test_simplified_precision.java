import com.trs.moye.bi.engine.indicator.util.CalculationUtils;

/**
 * 测试简化后的精度配置功能验证类
 * 
 * <AUTHOR>
 * @since 2025/08/12
 */
public class TestSimplifiedPrecision {
    
    public static void main(String[] args) {
        System.out.println("=== 测试简化后的精度配置功能 ===");
        
        // 测试 CalculationUtils 的基础功能
        System.out.println("1. CalculationUtils.calculateSafeRatio 测试:");
        String result1 = CalculationUtils.calculateSafeRatio(110, 100);
        System.out.println("   默认精度: " + result1); // 应该输出: 10.0%
        
        String result2 = CalculationUtils.calculateSafeRatio(110, 100, 2);
        System.out.println("   2位小数: " + result2); // 应该输出: 10.00%
        
        String result3 = CalculationUtils.calculateSafeRatio(110, 100, 0);
        System.out.println("   0位小数: " + result3); // 应该输出: 10%
        
        System.out.println("\n=== 方法简化总结 ===");
        System.out.println("已完成以下方法的简化：");
        
        System.out.println("\n2. ComparativeAnalysis 类:");
        System.out.println("   - getPeriodOverPeriodData(targetField, precision)");
        System.out.println("     * 移除了重载方法");
        System.out.println("     * precision 参数: required=false, defaultValue=\"1\"");
        
        System.out.println("   - getYearOverYearData(targetField, precision)");
        System.out.println("     * 移除了重载方法");
        System.out.println("     * precision 参数: required=false, defaultValue=\"1\"");
        
        System.out.println("   - getYoYByFields(field1, field2, precision)");
        System.out.println("     * 移除了重载方法");
        System.out.println("     * precision 参数: required=false, defaultValue=\"1\"");
        
        System.out.println("\n3. DataAggregation 类:");
        System.out.println("   - getRatioData(targetField, precision)");
        System.out.println("     * 移除了重载方法");
        System.out.println("     * precision 参数: required=false, defaultValue=\"1\"");
        
        System.out.println("\n=== 使用示例 ===");
        System.out.println("// 使用默认精度（1位小数）");
        System.out.println("ComparativeAnalysis.getPeriodOverPeriodData(\"sales\");");
        System.out.println("DataAggregation.getRatioData(\"sales\");");
        System.out.println("");
        System.out.println("// 使用自定义精度");
        System.out.println("ComparativeAnalysis.getPeriodOverPeriodData(\"sales\", 2);");
        System.out.println("DataAggregation.getRatioData(\"sales\", 0);");
        
        System.out.println("\n=== 优势 ===");
        System.out.println("1. 代码更简洁：移除了重复的重载方法");
        System.out.println("2. 参数可选：precision 参数有默认值，调用时可省略");
        System.out.println("3. 功能一致：保持了原有的所有功能");
        System.out.println("4. 易于维护：减少了代码重复，降低维护成本");
    }
}
