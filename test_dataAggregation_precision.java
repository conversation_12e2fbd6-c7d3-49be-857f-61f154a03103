import com.trs.moye.bi.engine.indicator.util.CalculationUtils;

/**
 * 测试 DataAggregation 精度配置功能的验证类
 * 
 * <AUTHOR>
 * @since 2025/08/12
 */
public class TestDataAggregationPrecision {
    
    public static void main(String[] args) {
        System.out.println("=== 测试 CalculationUtils.calculateSafeRatio ===");
        
        // 测试默认精度（应该是1位小数）
        System.out.println("测试默认精度:");
        String result1 = CalculationUtils.calculateSafeRatio(110, 100);
        System.out.println("110 vs 100 (默认精度): " + result1); // 应该输出: 10.0%
        
        // 测试自定义精度
        System.out.println("\n测试自定义精度:");
        String result2 = CalculationUtils.calculateSafeRatio(110, 100, 0);
        System.out.println("110 vs 100 (0位小数): " + result2); // 应该输出: 10%
        
        String result3 = CalculationUtils.calculateSafeRatio(110, 100, 2);
        System.out.println("110 vs 100 (2位小数): " + result3); // 应该输出: 10.00%
        
        String result4 = CalculationUtils.calculateSafeRatio(110, 100, 3);
        System.out.println("110 vs 100 (3位小数): " + result4); // 应该输出: 10.000%
        
        // 测试边界情况
        System.out.println("\n测试边界情况:");
        String result5 = CalculationUtils.calculateSafeRatio(110, 100, -1);
        System.out.println("110 vs 100 (负数精度): " + result5); // 应该使用默认精度: 10.0%
        
        String result6 = CalculationUtils.calculateSafeRatio(110, 100, 15);
        System.out.println("110 vs 100 (超大精度): " + result6); // 应该使用默认精度: 10.0%
        
        // 测试除零情况
        System.out.println("\n测试除零情况:");
        String result7 = CalculationUtils.calculateSafeRatio(110, 0);
        System.out.println("110 vs 0: " + result7); // 应该输出: /
        
        String result8 = CalculationUtils.calculateSafeRatio(null, 100);
        System.out.println("null vs 100: " + result8); // 应该输出: /
        
        System.out.println("\n=== DataAggregation 修改总结 ===");
        System.out.println("注意：DataAggregation 的方法需要在实际运行环境中测试，");
        System.out.println("因为它们依赖于 IndicatorDataContext 上下文。");
        System.out.println("\n主要修改包括：");
        System.out.println("1. getRatioData() 默认精度改为1位小数");
        System.out.println("2. getRatioData(targetField, precision) 支持自定义精度");
        System.out.println("3. dailyAverage() 默认精度改为1位小数");
        System.out.println("4. calculateSingleRatio() 内部方法支持精度配置");
        
        System.out.println("\n=== 使用示例 ===");
        System.out.println("// 使用默认精度（1位小数）");
        System.out.println("Map<String, Object> result1 = DataAggregation.getRatioData(\"sales\");");
        System.out.println("");
        System.out.println("// 使用自定义精度（2位小数）");
        System.out.println("Map<String, Object> result2 = DataAggregation.getRatioData(\"sales\", 2);");
        System.out.println("");
        System.out.println("// 使用整数显示（0位小数）");
        System.out.println("Map<String, Object> result3 = DataAggregation.getRatioData(\"revenue\", 0);");
    }
}
