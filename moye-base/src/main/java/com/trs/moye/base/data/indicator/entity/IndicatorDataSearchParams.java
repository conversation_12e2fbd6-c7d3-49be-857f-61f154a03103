package com.trs.moye.base.data.indicator.entity;

import com.trs.moye.base.data.indicator.enums.IndicatorSearchRange;
import com.trs.moye.base.data.service.entity.Condition;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 指标数据搜索参数
 *
 * <AUTHOR>
 * @since 2025/06/03 15:42:28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IndicatorDataSearchParams {

    /**
     * 搜索范围
     */
    private IndicatorSearchRange searchRange;

    /**
     * 数据型id
     */
    private Integer dataModelId;
    /**
     * 表名称
     */
    private String tableName;
    /**
     * 统计周期
     */
    private StatisticPeriod statisticPeriod;
    /**
     * 返回字段
     */
    private List<String> returnFields;
    /**
     * 条件
     */
    private List<Condition> conditions;
    /**
     * 排序字段
     */
    private IndicatorSortField sortField;
    /**
     * 目标周期列表，如果该值不为空，则只查询这一个周期的数据
     */
    private List<StatisticPeriod> targetPeriods;
    /**
     * 结果集大小
     */
    private Integer size;
    /**
     * 是否需要分组
     * <p>
     * 控制指标数据查询时是否执行分组逻辑：
     * <ul>
     *   <li>true：执行分组逻辑，根据统计维度对数据进行分组</li>
     *   <li>false或null：跳过分组逻辑，直接返回原始数据（默认行为）</li>
     * </ul>
     * </p>
     */
    private Boolean needGroup;
}
